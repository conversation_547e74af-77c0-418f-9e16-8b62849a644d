{% extends 'base.html.twig' %}

{% block title %}S<PERSON>rá<PERSON> uživatelů - Administrace{% endblock %}

{% block body %}
<div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{{ path('app_home') }}" class="text-xl font-bold text-indigo-600">Infauna</a>
                    <span class="ml-2 text-gray-500">/</span>
                    <a href="{{ path('app_admin') }}" class="ml-2 text-gray-500 hover:text-gray-700">Administrace</a>
                    <span class="ml-2 text-gray-500">/ Uživatelé</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">{{ app.user.fullName ?: app.user.email }}</span>
                    <a href="{{ path('app_logout') }}" class="text-gray-500 hover:text-gray-700">Odhlásit se</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Flash messages -->
        {% for label, messages in app.flashes %}
            {% for message in messages %}
                <div class="mb-4 rounded-md bg-{{ label == 'error' ? 'red' : 'green' }}-50 p-4">
                    <div class="text-sm text-{{ label == 'error' ? 'red' : 'green' }}-700">{{ message }}</div>
                </div>
            {% endfor %}
        {% endfor %}

        <!-- Page header -->
        <div class="md:flex md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    Správa uživatelů
                </h2>
            </div>
            <div class="mt-4 flex md:mt-0 md:ml-4">
                <a href="{{ path('app_admin') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Zpět na přehled
                </a>
            </div>
        </div>

        <!-- Users table -->
        <div class="mt-8 flex flex-col">
            <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                    <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Uživatel
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Kontakt
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Role
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Registrace
                                    </th>
                                    <th scope="col" class="relative px-6 py-3">
                                        <span class="sr-only">Akce</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for user in users %}
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <div class="h-10 w-10 rounded-full bg-indigo-500 flex items-center justify-center">
                                                        <span class="text-sm font-medium text-white">
                                                            {{ user.fullName ? user.fullName|slice(0, 1)|upper : user.email|slice(0, 1)|upper }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">
                                                        {{ user.fullName ?: 'Bez jména' }}
                                                    </div>
                                                    <div class="text-sm text-gray-500">
                                                        {{ user.email }}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ user.phoneNumber ?: '-' }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            {% if user.isAdmin %}
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                                    Administrátor
                                                </span>
                                            {% else %}
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                    Uživatel
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ user.createdAt|date('d.m.Y H:i') }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div class="flex space-x-2">
                                                {% if user != app.user %}
                                                    <form method="post" action="{{ path('app_admin_toggle_admin', {id: user.id}) }}" class="inline">
                                                        <input type="hidden" name="_token" value="{{ csrf_token('toggle-admin-' ~ user.id) }}">
                                                        <button type="submit" class="text-indigo-600 hover:text-indigo-900">
                                                            {{ user.isAdmin ? 'Odebrat admin' : 'Přidat admin' }}
                                                        </button>
                                                    </form>
                                                    <form method="post" action="{{ path('app_admin_delete_user', {id: user.id}) }}" class="inline" onsubmit="return confirm('Opravdu chcete smazat tohoto uživatele?')">
                                                        <input type="hidden" name="_token" value="{{ csrf_token('delete-user-' ~ user.id) }}">
                                                        <button type="submit" class="text-red-600 hover:text-red-900">
                                                            Smazat
                                                        </button>
                                                    </form>
                                                {% else %}
                                                    <span class="text-gray-400">Vy</span>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% else %}
                                    <tr>
                                        <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                            Žádní uživatelé nebyli nalezeni.
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
