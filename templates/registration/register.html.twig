{% extends 'base.html.twig' %}

{% block title %}Registrace{% endblock %}

{% block body %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Vytvoření nové<PERSON>
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Nebo
                <a href="{{ path('app_login') }}" class="font-medium text-indigo-600 hover:text-indigo-500">
                    se přihlaste do existuj<PERSON><PERSON><PERSON><PERSON>
                </a>
            </p>
        </div>

        {{ form_start(registrationForm, {'attr': {'class': 'mt-8 space-y-6'}}) }}
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        {{ form_label(registrationForm.firstName, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700'}}) }}
                        {{ form_widget(registrationForm.firstName) }}
                        {{ form_errors(registrationForm.firstName) }}
                    </div>
                    <div>
                        {{ form_label(registrationForm.lastName, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700'}}) }}
                        {{ form_widget(registrationForm.lastName) }}
                        {{ form_errors(registrationForm.lastName) }}
                    </div>
                </div>

                <div>
                    {{ form_label(registrationForm.email, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700'}}) }}
                    {{ form_widget(registrationForm.email) }}
                    {{ form_errors(registrationForm.email) }}
                </div>

                <div>
                    {{ form_label(registrationForm.phoneNumber, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700'}}) }}
                    {{ form_widget(registrationForm.phoneNumber) }}
                    {{ form_errors(registrationForm.phoneNumber) }}
                </div>

                <div>
                    {{ form_label(registrationForm.plainPassword.first, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700'}}) }}
                    {{ form_widget(registrationForm.plainPassword.first) }}
                    {{ form_errors(registrationForm.plainPassword.first) }}
                </div>

                <div>
                    {{ form_label(registrationForm.plainPassword.second, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700'}}) }}
                    {{ form_widget(registrationForm.plainPassword.second) }}
                    {{ form_errors(registrationForm.plainPassword.second) }}
                </div>

                <div class="flex items-center">
                    {{ form_widget(registrationForm.agreeTerms) }}
                    {{ form_label(registrationForm.agreeTerms, null, {'label_attr': {'class': 'ml-2 block text-sm text-gray-900'}}) }}
                </div>
                {{ form_errors(registrationForm.agreeTerms) }}
            </div>

            <div>
                <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <svg class="h-5 w-5 text-indigo-500 group-hover:text-indigo-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6z" />
                        </svg>
                    </span>
                    Registrovat se
                </button>
            </div>
        {{ form_end(registrationForm) }}
    </div>
</div>
{% endblock %}
