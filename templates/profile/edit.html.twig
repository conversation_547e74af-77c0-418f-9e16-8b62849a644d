{% extends 'base.html.twig' %}

{% block title %}Upravit profil{% endblock %}

{% block body %}
<div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    {{ include('_partials/navigation.html.twig') }}

    <div class="max-w-3xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Flash messages -->
        {{ include('_partials/flash_messages.html.twig') }}

        <!-- Page header -->
        <div class="md:flex md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    Upravit profil
                </h2>
            </div>
            <div class="mt-4 flex md:mt-0 md:ml-4">
                <a href="{{ path('app_profile') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Zpět na profil
                </a>
            </div>
        </div>

        <!-- Edit form -->
        <div class="mt-8">
            <div class="bg-white shadow sm:rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    {{ form_start(form) }}
                        <div class="grid grid-cols-1 gap-6">
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    {{ form_label(form.firstName, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700'}}) }}
                                    {{ form_widget(form.firstName) }}
                                    {{ form_errors(form.firstName) }}
                                </div>
                                <div>
                                    {{ form_label(form.lastName, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700'}}) }}
                                    {{ form_widget(form.lastName) }}
                                    {{ form_errors(form.lastName) }}
                                </div>
                            </div>

                            <div>
                                {{ form_label(form.email, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700'}}) }}
                                {{ form_widget(form.email) }}
                                {{ form_errors(form.email) }}
                            </div>

                            <div>
                                {{ form_label(form.phoneNumber, null, {'label_attr': {'class': 'block text-sm font-medium text-gray-700'}}) }}
                                {{ form_widget(form.phoneNumber) }}
                                {{ form_errors(form.phoneNumber) }}
                            </div>
                        </div>

                        <div class="mt-6 flex justify-end space-x-3">
                            <a href="{{ path('app_profile') }}" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Zrušit
                            </a>
                            <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Uložit změny
                            </button>
                        </div>
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
