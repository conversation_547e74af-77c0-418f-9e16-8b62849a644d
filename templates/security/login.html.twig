{% extends 'base.html.twig' %}

{% block title %}<PERSON><PERSON><PERSON><PERSON><PERSON>šení{% endblock %}

{% block body %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Přihlášení do účtu
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Nebo
                <a href="{{ path('app_register') }}" class="font-medium text-indigo-600 hover:text-indigo-500">
                    si vytvořte nový účet
                </a>
            </p>
        </div>

        {% if error %}
            <div class="rounded-md bg-red-50 p-4">
                <div class="flex">
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">
                            <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>
                        </h3>
                        <div class="mt-2 text-sm text-red-700">
                            <p>{{ error.messageKey|trans(error.messageData, 'security') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        {{ form_start(form, {'attr': {'class': 'mt-8 space-y-6'}}) }}
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    {{ form_label(form.email, null, {'label_attr': {'class': 'sr-only'}}) }}
                    {{ form_widget(form.email, {'attr': {'class': 'appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm', 'placeholder': 'Email adresa'}}) }}
                </div>
                <div>
                    {{ form_label(form.password, null, {'label_attr': {'class': 'sr-only'}}) }}
                    {{ form_widget(form.password, {'attr': {'class': 'appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm', 'placeholder': 'Heslo'}}) }}
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    {{ form_widget(form._remember_me, {'attr': {'class': 'h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded'}}) }}
                    {{ form_label(form._remember_me, null, {'label_attr': {'class': 'ml-2 block text-sm text-gray-900'}}) }}
                </div>

                <div class="text-sm">
                    <a href="#" class="font-medium text-indigo-600 hover:text-indigo-500">
                        Zapomněli jste heslo?
                    </a>
                </div>
            </div>

            <div>
                <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <svg class="h-5 w-5 text-indigo-500 group-hover:text-indigo-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                        </svg>
                    </span>
                    Přihlásit se
                </button>
            </div>
        {{ form_end(form) }}
    </div>
</div>
{% endblock %}
