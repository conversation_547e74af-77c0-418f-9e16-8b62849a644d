{% extends 'base.html.twig' %}

{% block title %}Upravit inzerát - {{ post.title }}{% endblock %}

{% block body %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center">
                    <a href="{{ path('app_user_posts') }}"
                       class="text-gray-400 hover:text-gray-600 mr-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <h1 class="text-2xl font-bold text-gray-900">Upravit inzerát</h1>
                </div>
            </div>
        </div>

        <!-- Current Images -->
        {% if post.images is not empty %}
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Současné fotografie</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {% for image in post.images %}
                            <div class="relative">
                                <img src="{{ asset('uploads/posts/' ~ image.imageUrl) }}"
                                     alt="{{ image.imageUrl }}"
                                     class="w-full h-24 object-cover rounded-lg">
                                <div class="absolute top-1 right-1">
                                    <button type="button"
                                            class="bg-red-600 text-white rounded-full p-1 text-xs hover:bg-red-700"
                                            onclick="removeImage({{ image.id }})"
                                            data-image-id="{{ image.id }}">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- Form -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-6">
                {{ form_start(form, {'attr': {'enctype': 'multipart/form-data'}}) }}

                <div class="space-y-6">
                    <!-- Basic Information -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Základní informace</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="md:col-span-2">
                                {{ form_label(form.title) }}
                                {{ form_widget(form.title) }}
                                {{ form_errors(form.title) }}
                            </div>

                            <div>
                                {{ form_label(form.postType) }}
                                {{ form_widget(form.postType) }}
                                {{ form_errors(form.postType) }}
                            </div>

                            <div>
                                {{ form_label(form.country) }}
                                {{ form_widget(form.country) }}
                                {{ form_errors(form.country) }}
                            </div>

                            <div>
                                {{ form_label(form.region) }}
                                {{ form_widget(form.region) }}
                                {{ form_errors(form.region) }}
                            </div>

                            <div>
                                {{ form_label(form.category) }}
                                {{ form_widget(form.category) }}
                                {{ form_errors(form.category) }}
                            </div>

                            <div class="md:col-span-2">
                                {{ form_label(form.breed) }}
                                {{ form_widget(form.breed) }}
                                {{ form_errors(form.breed) }}
                            </div>
                        </div>
                    </div>

                    <!-- Price Information -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Cena</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                {{ form_label(form.priceType) }}
                                {{ form_widget(form.priceType) }}
                                {{ form_errors(form.priceType) }}
                            </div>

                            <div>
                                {{ form_label(form.priceAmount) }}
                                {{ form_widget(form.priceAmount) }}
                                {{ form_errors(form.priceAmount) }}
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Popis</h3>
                        {{ form_label(form.description) }}
                        {{ form_widget(form.description) }}
                        {{ form_errors(form.description) }}
                    </div>

                    <!-- Contact Information -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Kontaktní informace</h3>
                        {{ form_label(form.contactPhone) }}
                        {{ form_widget(form.contactPhone) }}
                        {{ form_errors(form.contactPhone) }}
                        <p class="mt-1 text-sm text-gray-500">Pokud nevyplníte, použije se telefon z vašeho profilu.</p>
                    </div>

                    <!-- Images -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Přidat další fotografie</h3>
                        {{ form_label(form.images) }}
                        {{ form_widget(form.images) }}
                        {{ form_errors(form.images) }}
                        <p class="mt-1 text-sm text-gray-500">Můžete nahrát další fotografie. Podporované formáty: JPEG, PNG, WebP. Maximální velikost: 5MB na soubor.</p>
                    </div>
                </div>

                <!-- Actions -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ path('app_user_posts') }}"
                       class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Zrušit
                    </a>
                    <button type="submit"
                            class="bg-indigo-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Uložit změny
                    </button>
                </div>

                {{ form_end(form) }}
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Dynamic breed filtering based on category
    const categorySelect = document.querySelector('#post_form_category');
    const breedSelect = document.querySelector('#post_form_breed');

    if (categorySelect && breedSelect) {
        categorySelect.addEventListener('change', function() {
            const categoryId = this.value;
            const currentValue = breedSelect.value;

            // Clear current breed options
            breedSelect.innerHTML = '<option value="">Vyberte plemeno</option>';

            if (categoryId) {
                breedSelect.disabled = true; // Disable while loading

                fetch(`/moje-inzeraty/api/breeds/${categoryId}`)
                    .then(response => response.json())
                    .then(breeds => {
                        breeds.forEach(breed => {
                            const option = document.createElement('option');
                            option.value = breed.id;
                            option.textContent = breed.name;
                            // Restore selection if it matches
                            if (breed.id == currentValue) {
                                option.selected = true;
                            }
                            breedSelect.appendChild(option);
                        });
                        breedSelect.disabled = false;
                    })
                    .catch(error => {
                        console.error('Error loading breeds:', error);
                        breedSelect.disabled = false;
                    });
            } else {
                breedSelect.disabled = true;
            }
        });
    }

    // Price type handling
    const priceTypeSelect = document.querySelector('#post_form_priceType');
    const priceAmountInput = document.querySelector('#post_form_priceAmount');

    if (priceTypeSelect && priceAmountInput) {
        priceTypeSelect.addEventListener('change', function() {
            const priceType = this.value;

            if (priceType && (priceType.includes('Kč') || priceType.includes('€'))) {
                priceAmountInput.disabled = false;
                priceAmountInput.required = true;
            } else {
                priceAmountInput.disabled = true;
                priceAmountInput.required = false;
                if (!priceType) {
                    priceAmountInput.value = '';
                }
            }
        });

        // Trigger change event on page load
        priceTypeSelect.dispatchEvent(new Event('change'));
    }
});

function removeImage(imageId) {
    if (confirm('Opravdu chcete smazat tuto fotografii?')) {
        const button = event.target.closest('button');
        const imageContainer = button.closest('.relative');

        // Get CSRF token
        const csrfToken = document.querySelector('input[name="_token"]').value;

        fetch(`/moje-inzeraty/image/${imageId}/remove`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                imageContainer.style.display = 'none';
                // Show success message
                showFlashMessage('Fotografie byla smazána.', 'success');
            } else {
                alert(data.error || 'Chyba při mazání fotografie.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Chyba při mazání fotografie.');
        });
    }
}

function showFlashMessage(message, type) {
    // Create flash message element
    const flashDiv = document.createElement('div');
    flashDiv.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg ${type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`;
    flashDiv.textContent = message;

    document.body.appendChild(flashDiv);

    // Remove after 3 seconds
    setTimeout(() => {
        flashDiv.remove();
    }, 3000);
}
</script>
{% endblock %}
