{% extends 'base.html.twig' %}

{% block title %}<PERSON><PERSON> in<PERSON>{% endblock %}

{% block body %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h1 class="text-2xl font-bold text-gray-900">Moje inzeráty</h1>
                    <a href="{{ path('app_user_post_new') }}"
                       class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Nový inzerát
                    </a>
                </div>
            </div>
        </div>

        {% if posts is empty %}
            <!-- Empty state -->
            <div class="bg-white shadow rounded-lg">
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Žádné inzeráty</h3>
                    <p class="mt-1 text-sm text-gray-500">Začněte vytvořením svého prvního inzerátu.</p>
                    <div class="mt-6">
                        <a href="{{ path('app_user_post_new') }}"
                           class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            Nový inzerát
                        </a>
                    </div>
                </div>
            </div>
        {% else %}
            <!-- Posts grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for post in posts %}
                    <div class="bg-white shadow rounded-lg overflow-hidden">
                        <!-- Image -->
                        <div class="h-48 bg-gray-200 relative">
                            {% if post.mainImage %}
                                <img src="{{ asset('uploads/posts/' ~ post.mainImage.imageUrl) }}"
                                     alt="{{ post.title }}"
                                     class="w-full h-full object-cover">
                            {% else %}
                                <div class="flex items-center justify-center h-full">
                                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            {% endif %}

                            <!-- Status badges -->
                            <div class="absolute top-2 left-2 flex flex-col space-y-1">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{{ post.postType.value == 'prodám' ? 'green' : (post.postType.value == 'koupím' ? 'blue' : 'purple') }}-100 text-{{ post.postType.value == 'prodám' ? 'green' : (post.postType.value == 'koupím' ? 'blue' : 'purple') }}-800">
                                    {{ post.postType.label }}
                                </span>
                                {% if post.isArchived %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Archivováno
                                    </span>
                                {% elseif not post.isActive %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Neaktivní
                                    </span>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="p-4">
                            <h3 class="text-lg font-medium text-gray-900 mb-2">{{ post.title }}</h3>
                            <p class="text-sm text-gray-600 mb-2">{{ post.category.name }}</p>
                            {% if post.formattedPrice %}
                                <p class="text-lg font-semibold text-indigo-600 mb-3">{{ post.formattedPrice }}</p>
                            {% endif %}
                            <p class="text-sm text-gray-500 mb-4">{{ post.description|length > 100 ? post.description|slice(0, 100) ~ '...' : post.description }}</p>

                            <!-- Actions -->
                            <div class="flex justify-between items-center">
                                <div class="flex space-x-2">
                                    <a href="{{ path('app_user_post_edit', {id: post.id}) }}"
                                       class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                                        Upravit
                                    </a>
                                    <a href="{{ path('app_post_detail', {slug: post.slug}) }}"
                                       class="text-gray-600 hover:text-gray-900 text-sm font-medium">
                                        Zobrazit
                                    </a>
                                </div>

                                <div class="flex space-x-1">
                                    {% if post.isArchived %}
                                        <form method="post" action="{{ path('app_user_post_unarchive', {id: post.id}) }}" class="inline">
                                            <input type="hidden" name="_token" value="{{ csrf_token('unarchive' ~ post.id) }}">
                                            <button type="submit"
                                                    class="text-green-600 hover:text-green-900 text-sm font-medium"
                                                    onclick="return confirm('Opravdu chcete obnovit tento inzerát?')">
                                                Obnovit
                                            </button>
                                        </form>
                                    {% else %}
                                        <form method="post" action="{{ path('app_user_post_toggle_status', {id: post.id}) }}" class="inline">
                                            <input type="hidden" name="_token" value="{{ csrf_token('toggle_status' ~ post.id) }}">
                                            <button type="submit"
                                                    class="text-{{ post.isActive ? 'orange' : 'green' }}-600 hover:text-{{ post.isActive ? 'orange' : 'green' }}-900 text-sm font-medium">
                                                {{ post.isActive ? 'Deaktivovat' : 'Aktivovat' }}
                                            </button>
                                        </form>
                                        <form method="post" action="{{ path('app_user_post_archive', {id: post.id}) }}" class="inline">
                                            <input type="hidden" name="_token" value="{{ csrf_token('archive' ~ post.id) }}">
                                            <button type="submit"
                                                    class="text-yellow-600 hover:text-yellow-900 text-sm font-medium"
                                                    onclick="return confirm('Opravdu chcete archivovat tento inzerát?')">
                                                Archivovat
                                            </button>
                                        </form>
                                    {% endif %}

                                    <form method="post" action="{{ path('app_user_post_delete', {id: post.id}) }}" class="inline">
                                        <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ post.id) }}">
                                        <button type="submit"
                                                class="text-red-600 hover:text-red-900 text-sm font-medium"
                                                onclick="return confirm('Opravdu chcete smazat tento inzerát? Tato akce je nevratná.')">
                                            Smazat
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
