{% extends 'base.html.twig' %}

{% block title %}Nový inzerát{% endblock %}

{% block body %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center">
                    <a href="{{ path('app_user_posts') }}"
                       class="text-gray-400 hover:text-gray-600 mr-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <h1 class="text-2xl font-bold text-gray-900">Nový inzerát</h1>
                </div>
            </div>
        </div>

        <!-- Form -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-6">
                {{ form_start(form, {'attr': {'enctype': 'multipart/form-data'}}) }}

                <div class="space-y-6">
                    <!-- Basic Information -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Základní informace</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="md:col-span-2">
                                {{ form_label(form.title) }}
                                {{ form_widget(form.title) }}
                                {{ form_errors(form.title) }}
                            </div>

                            <div>
                                {{ form_label(form.postType) }}
                                {{ form_widget(form.postType) }}
                                {{ form_errors(form.postType) }}
                            </div>

                            <div>
                                {{ form_label(form.country) }}
                                {{ form_widget(form.country) }}
                                {{ form_errors(form.country) }}
                            </div>

                            <div>
                                {{ form_label(form.region) }}
                                {{ form_widget(form.region) }}
                                {{ form_errors(form.region) }}
                            </div>

                            <div>
                                {{ form_label(form.category) }}
                                {{ form_widget(form.category) }}
                                {{ form_errors(form.category) }}
                            </div>

                            <div class="md:col-span-2">
                                {{ form_label(form.breed) }}
                                {{ form_widget(form.breed) }}
                                {{ form_errors(form.breed) }}
                            </div>
                        </div>
                    </div>

                    <!-- Price Information -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Cena</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                {{ form_label(form.priceType) }}
                                {{ form_widget(form.priceType) }}
                                {{ form_errors(form.priceType) }}
                            </div>

                            <div>
                                {{ form_label(form.priceAmount) }}
                                {{ form_widget(form.priceAmount) }}
                                {{ form_errors(form.priceAmount) }}
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Popis</h3>
                        {{ form_label(form.description) }}
                        {{ form_widget(form.description) }}
                        {{ form_errors(form.description) }}
                    </div>

                    <!-- Contact Information -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Kontaktní informace</h3>
                        {{ form_label(form.contactPhone) }}
                        {{ form_widget(form.contactPhone) }}
                        {{ form_errors(form.contactPhone) }}
                        <p class="mt-1 text-sm text-gray-500">Pokud nevyplníte, použije se telefon z vašeho profilu.</p>
                    </div>

                    <!-- Images -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Fotografie</h3>
                        {{ form_label(form.images) }}
                        {{ form_widget(form.images) }}
                        {{ form_errors(form.images) }}
                        <p class="mt-1 text-sm text-gray-500">Můžete nahrát až 10 fotografií. Podporované formáty: JPEG, PNG, WebP. Maximální velikost: 5MB na soubor.</p>
                    </div>
                </div>

                <!-- Actions -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ path('app_user_posts') }}"
                       class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Zrušit
                    </a>
                    <button type="submit"
                            class="bg-indigo-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Vytvořit inzerát
                    </button>
                </div>

                {{ form_end(form) }}
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Dynamic breed filtering based on category
    const categorySelect = document.querySelector('#post_form_category');
    const breedSelect = document.querySelector('#post_form_breed');

    if (categorySelect && breedSelect) {
        categorySelect.addEventListener('change', function() {
            const categoryId = this.value;

            // Clear current breed options
            breedSelect.innerHTML = '<option value="">Vyberte plemeno</option>';

            if (categoryId) {
                breedSelect.disabled = true; // Disable while loading

                fetch(`/moje-inzeraty/api/breeds/${categoryId}`)
                    .then(response => response.json())
                    .then(breeds => {
                        breeds.forEach(breed => {
                            const option = document.createElement('option');
                            option.value = breed.id;
                            option.textContent = breed.name;
                            breedSelect.appendChild(option);
                        });
                        breedSelect.disabled = false;
                    })
                    .catch(error => {
                        console.error('Error loading breeds:', error);
                        breedSelect.disabled = false;
                    });
            } else {
                breedSelect.disabled = true;
            }
        });
    }

    // Price type handling
    const priceTypeSelect = document.querySelector('#post_form_priceType');
    const priceAmountInput = document.querySelector('#post_form_priceAmount');

    if (priceTypeSelect && priceAmountInput) {
        priceTypeSelect.addEventListener('change', function() {
            const priceType = this.value;

            // Enable/disable price amount based on price type
            if (priceType && (priceType.includes('Kč') || priceType.includes('€'))) {
                priceAmountInput.disabled = false;
                priceAmountInput.required = true;
            } else {
                priceAmountInput.disabled = true;
                priceAmountInput.required = false;
                priceAmountInput.value = '';
            }
        });

        // Trigger change event on page load
        priceTypeSelect.dispatchEvent(new Event('change'));
    }
});
</script>
{% endblock %}
