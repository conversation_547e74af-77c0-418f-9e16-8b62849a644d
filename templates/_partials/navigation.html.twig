<nav class="bg-white shadow-lg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <a href="{{ path('app_home') }}" class="text-2xl font-bold text-indigo-600">Infauna</a>
                </div>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="{{ path('app_home') }}" class="text-gray-900 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">Domů</a>
                        <a href="{{ path('app_posts') }}" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">Inzeráty</a>
                    </div>
                </div>
            </div>
            <div class="hidden md:block">
                <div class="ml-4 flex items-center md:ml-6 space-x-4">
                    {% if app.user %}
                        <a href="{{ path('app_user_posts') }}" class="text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">
                            Moje inzeráty
                        </a>
                        <a href="{{ path('app_profile') }}" class="text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">
                            {{ app.user.fullName ?: app.user.email }}
                        </a>
                        {% if app.user.isAdmin %}
                            <a href="{{ path('app_admin') }}" class="text-indigo-600 hover:text-indigo-700 px-3 py-2 rounded-md text-sm font-medium">
                                Administrace
                            </a>
                        {% endif %}
                        <a href="{{ path('app_logout') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                            Odhlásit se
                        </a>
                    {% else %}
                        <a href="{{ path('app_login') }}" class="text-indigo-600 hover:text-indigo-700 px-3 py-2 rounded-md text-sm font-medium">
                            Přihlásit se
                        </a>
                        <a href="{{ path('app_register') }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                            Registrace
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</nav>
