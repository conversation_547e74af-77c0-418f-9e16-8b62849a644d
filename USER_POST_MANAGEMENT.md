# Spr<PERSON>va inzerátů uživatelem

Byla implementována kompletní funkcionalita pro správu inzerátů uživateli včetně vytváření, editace a archivace.

## Implementované funkce

### 1. **Vytváření inzerátů**
- **URL**: `/moje-inzeraty/novy`
- **Formulář** obsahuje všechna potřebná pole:
  - Název inzerátu
  - Typ inzer<PERSON>tu (Prodám/Koupím/Daruji)
  - Země
  - Kraj
  - Kategorie
  - Plemeno (dynamicky načítané podle kategorie)
  - Typ ceny a částka
  - Popis
  - Kontaktní telefon
  - Nahrávání fotografií (až 10 ks)

### 2. **Editace inzerátů**
- **URL**: `/moje-inzeraty/{id}/upravit`
- Stejný formulář jako pro vytváření
- Zobrazení současných fotografií s možností mazání
- AJAX mazání fotografií bez reload stránky
- Dynamické načítání plemen podle kategorie

### 3. **Archivace inzerátů**
- **URL**: `/moje-inzeraty/{id}/archivovat` (POST)
- Inzerát se označí jako archivovaný a neaktivní
- Nezobrazuje se ve veřejných výpisech
- Možnost obnovení

### 4. **Obnovení archivovaných inzerátů**
- **URL**: `/moje-inzeraty/{id}/obnovit` (POST)
- Obnovení archivovaného inzerátu do aktivního stavu

### 5. **Mazání inzerátů**
- **URL**: `/moje-inzeraty/{id}/smazat` (POST)
- Úplné smazání inzerátu včetně fotografií
- Nevratná operace

### 6. **Přehled uživatelových inzerátů**
- **URL**: `/moje-inzeraty`
- Zobrazení všech inzerátů uživatele (včetně archivovaných)
- Barevné označení stavu inzerátů
- Rychlé akce (upravit, archivovat, smazat)

## Technické detaily

### Nové soubory
- `src/Controller/UserPostController.php` - Controller pro správu inzerátů
- `src/Form/PostFormType.php` - Formulář pro vytváření/editaci inzerátů
- `src/Service/PostService.php` - Service pro operace s inzeráty
- `templates/user_post/index.html.twig` - Přehled inzerátů uživatele
- `templates/user_post/new.html.twig` - Formulář pro nový inzerát
- `templates/user_post/edit.html.twig` - Formulář pro editaci inzerátu

### Upravené soubory
- `src/Entity/Post.php` - Přidáno pole `isArchived`
- `src/Repository/PostRepository.php` - Filtrování archivovaných inzerátů
- `src/Controller/PostController.php` - Vyloučení archivovaných z detailu
- `templates/_partials/navigation.html.twig` - Přidán odkaz "Moje inzeráty"
- `templates/post/index.html.twig` - Přidáno tlačítko "Přidat inzerát"
- `config/packages/security.yaml` - Zabezpečení nových rout

### Databázové změny
- Migrace `Version20250802150750.php` - Přidání sloupce `is_archived`

## Bezpečnost

- Všechny operace jsou chráněny `ROLE_USER`
- CSRF ochrana pro všechny formuláře a akce
- Kontrola vlastnictví inzerátu před editací/mazáním
- Validace nahrávaných souborů (typ, velikost)

## API Endpointy

### Načítání plemen podle kategorie
- **URL**: `/moje-inzeraty/api/breeds/{categoryId}`
- **Metoda**: GET
- **Odpověď**: JSON seznam plemen

### Mazání fotografií
- **URL**: `/moje-inzeraty/image/{id}/remove`
- **Metoda**: DELETE
- **Headers**: X-CSRF-Token
- **Odpověď**: JSON s výsledkem

## JavaScript funkce

- Dynamické načítání plemen podle vybrané kategorie
- AJAX mazání fotografií
- Podmíněné zobrazení pole pro cenu podle typu ceny
- Flash zprávy pro uživatelské akce

## Nahrávání souborů

- Adresář: `public/uploads/posts/`
- Podporované formáty: JPEG, PNG, WebP
- Maximální velikost: 5MB na soubor
- Maximální počet: 10 fotografií na inzerát
- Automatické generování unikátních názvů souborů

## Navigace

- Hlavní menu obsahuje odkaz "Moje inzeráty" pro přihlášené uživatele
- Na stránce inzerátů je tlačítko "Přidat inzerát" pro přihlášené uživatele
- Breadcrumb navigace ve formulářích

## Responsive design

- Všechny stránky jsou optimalizované pro mobilní zařízení
- Grid layout pro přehled inzerátů
- Mobilní menu (pokud bylo implementováno v základní šabloně)

## Testování

Pro otestování funkcionality:

1. Přihlaste se jako uživatel
2. Přejděte na "Moje inzeráty" v menu
3. Vytvořte nový inzerát pomocí tlačítka "Nový inzerát"
4. Vyzkoušejte editaci, archivaci a mazání inzerátů
5. Ověřte, že archivované inzeráty se nezobrazují ve veřejném výpisu

## Možná rozšíření

- Hromadné operace (označit více inzerátů)
- Kopírování inzerátu
- Statistiky inzerátů
- Notifikace o expiraci
- Automatická archivace starých inzerátů
- Drag & drop pro změnu pořadí fotografií
