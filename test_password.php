<?php

require_once 'vendor/autoload.php';

use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasher;
use Symfony\Component\PasswordHasher\PasswordHasherInterface;
use App\Entity\User;

// Simulace uživatele
$user = new User();
$user->setEmail('<EMAIL>');

// Hash z databáze
$hashFromDb = '$2y$13$oQKfFuuIWr21uoWlXsolVu1nlH4X5HnYiiA3n.LDYtBtImPFV9ysG';

// Testování různých hesel
$testPasswords = [
    'password',
    'admin',
    'admin123',
    '123456',
    'infauna',
    'test',
    'heslo'
];

echo "Testování <NAME_EMAIL>:\n";
echo "Hash z databáze: " . $hashFromDb . "\n\n";

foreach ($testPasswords as $password) {
    $isValid = password_verify($password, $hashFromDb);
    echo "Heslo '$password': " . ($isValid ? "✓ SPRÁVNÉ" : "✗ špatné") . "\n";
}

// Test s defaultním hashem
$defaultHash = '$2y$13$defaultpasswordhash';
echo "\n\nTestování defaultního hashe:\n";
echo "Hash: " . $defaultHash . "\n";

foreach ($testPasswords as $password) {
    $isValid = password_verify($password, $defaultHash);
    echo "Heslo '$password': " . ($isValid ? "✓ SPRÁVNÉ" : "✗ špatné") . "\n";
}
