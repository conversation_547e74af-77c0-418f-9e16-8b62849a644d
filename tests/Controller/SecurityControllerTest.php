<?php

namespace App\Tests\Controller;

use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class SecurityControllerTest extends WebTestCase
{

    public function testLoginPage(): void
    {
        $client = static::createClient();
        $crawler = $client->request('GET', '/login');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('h2', 'Přihlášení do účtu');
    }

    public function testRegistrationPage(): void
    {
        $client = static::createClient();
        $crawler = $client->request('GET', '/register');

        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('h2', 'Vytvoření nov<PERSON>ho <PERSON>');
    }

    public function testSuccessfulLogin(): void
    {
        $client = static::createClient();
        $entityManager = static::getContainer()->get('doctrine')->getManager();
        $passwordHasher = static::getContainer()->get(UserPasswordHasherInterface::class);

        // Create a test user
        $user = new User();
        $user->setEmail('<EMAIL>');
        $user->setPassword($passwordHasher->hashPassword($user, 'password123'));
        $user->setFirstName('Test');
        $user->setLastName('User');

        $entityManager->persist($user);
        $entityManager->flush();

        // Try to login
        $crawler = $client->request('GET', '/login');
        $form = $crawler->selectButton('Přihlásit se')->form([
            'login_form[email]' => '<EMAIL>',
            'login_form[password]' => 'password123',
        ]);

        $client->submit($form);

        $this->assertResponseRedirects('/');
        $client->followRedirect();
        $this->assertResponseIsSuccessful();

        // Clean up
        $entityManager->remove($user);
        $entityManager->flush();
    }

    public function testAdminAccess(): void
    {
        $client = static::createClient();
        $entityManager = static::getContainer()->get('doctrine')->getManager();
        $passwordHasher = static::getContainer()->get(UserPasswordHasherInterface::class);

        // Create an admin user
        $admin = new User();
        $admin->setEmail('<EMAIL>');
        $admin->setPassword($passwordHasher->hashPassword($admin, 'admin123'));
        $admin->setRoles(['ROLE_ADMIN']);
        $admin->setFirstName('Admin');
        $admin->setLastName('User');

        $entityManager->persist($admin);
        $entityManager->flush();

        // Login as admin
        $client->loginUser($admin);

        // Try to access admin area
        $client->request('GET', '/admin');
        $this->assertResponseIsSuccessful();
        $this->assertSelectorTextContains('h2', 'Administrace');

        // Clean up
        $entityManager->remove($admin);
        $entityManager->flush();
    }

    public function testNonAdminCannotAccessAdmin(): void
    {
        $client = static::createClient();
        $entityManager = static::getContainer()->get('doctrine')->getManager();
        $passwordHasher = static::getContainer()->get(UserPasswordHasherInterface::class);

        // Create a regular user
        $user = new User();
        $user->setEmail('<EMAIL>');
        $user->setPassword($passwordHasher->hashPassword($user, 'user123'));
        $user->setFirstName('Regular');
        $user->setLastName('User');

        $entityManager->persist($user);
        $entityManager->flush();

        // Login as regular user
        $client->loginUser($user);

        // Try to access admin area
        $client->request('GET', '/admin');
        $this->assertResponseStatusCodeSame(403);

        // Clean up
        $entityManager->remove($user);
        $entityManager->flush();
    }
}
