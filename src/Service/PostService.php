<?php

namespace App\Service;

use App\Entity\Post;
use App\Entity\PostImage;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\String\Slugger\SluggerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

class PostService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private SluggerInterface $slugger,
        #[Autowire('%kernel.project_dir%/public/uploads/posts')] private string $uploadsDirectory
    ) {
    }

    public function createPost(Post $post, User $user, array $uploadedFiles = []): Post
    {
        $post->setUser($user);
        $post->setSlug($this->generateUniqueSlug($post->getTitle()));

        $this->entityManager->persist($post);

        if (!empty($uploadedFiles)) {
            $this->handleImageUploads($post, $uploadedFiles);
        }

        $this->entityManager->flush();

        return $post;
    }

    public function updatePost(Post $post, array $uploadedFiles = []): Post
    {
        // Update slug if title changed
        $post->setSlug($this->generateUniqueSlug($post->getTitle(), $post->getId()));

        if (!empty($uploadedFiles)) {
            $this->handleImageUploads($post, $uploadedFiles);
        }

        $this->entityManager->flush();

        return $post;
    }

    public function archivePost(Post $post): Post
    {
        $post->setIsArchived(true);
        $post->setIsActive(false);

        $this->entityManager->flush();

        return $post;
    }

    public function unarchivePost(Post $post): Post
    {
        $post->setIsArchived(false);
        $post->setIsActive(true);

        $this->entityManager->flush();

        return $post;
    }

    public function deletePost(Post $post): void
    {
        // Delete associated images from filesystem
        foreach ($post->getImages() as $image) {
            $this->deleteImageFile($image);
        }

        $this->entityManager->remove($post);
        $this->entityManager->flush();
    }

    private function generateUniqueSlug(string $title, ?int $excludeId = null): string
    {
        $baseSlug = $this->slugger->slug($title)->lower();
        $slug = $baseSlug;
        $counter = 1;

        while ($this->slugExists($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    private function slugExists(string $slug, ?int $excludeId = null): bool
    {
        $qb = $this->entityManager->getRepository(Post::class)
            ->createQueryBuilder('p')
            ->select('COUNT(p.id)')
            ->where('p.slug = :slug')
            ->setParameter('slug', $slug);

        if ($excludeId) {
            $qb->andWhere('p.id != :excludeId')
               ->setParameter('excludeId', $excludeId);
        }

        return $qb->getQuery()->getSingleScalarResult() > 0;
    }

    private function handleImageUploads(Post $post, array $uploadedFiles): void
    {
        if (!is_dir($this->uploadsDirectory)) {
            mkdir($this->uploadsDirectory, 0755, true);
        }

        $order = $post->getImages()->count();

        foreach ($uploadedFiles as $uploadedFile) {
            if ($uploadedFile instanceof UploadedFile) {
                $originalFilename = pathinfo($uploadedFile->getClientOriginalName(), PATHINFO_FILENAME);
                $safeFilename = $this->slugger->slug($originalFilename);
                $newFilename = $safeFilename . '-' . uniqid() . '.' . $uploadedFile->guessExtension();

                $uploadedFile->move($this->uploadsDirectory, $newFilename);

                $postImage = new PostImage();
                $postImage->setPost($post);
                $postImage->setImageUrl($newFilename);
                $postImage->setOrder($order);

                $this->entityManager->persist($postImage);
                $order++;
            }
        }
    }

    private function deleteImageFile(PostImage $image): void
    {
        $filePath = $this->uploadsDirectory . '/' . $image->getImageUrl();
        if (file_exists($filePath)) {
            unlink($filePath);
        }
    }

    public function getUserPosts(User $user, bool $includeArchived = false): array
    {
        $qb = $this->entityManager->getRepository(Post::class)
            ->createQueryBuilder('p')
            ->where('p.user = :user')
            ->setParameter('user', $user)
            ->orderBy('p.createdAt', 'DESC');

        if (!$includeArchived) {
            $qb->andWhere('p.isArchived = :archived')
               ->setParameter('archived', false);
        }

        return $qb->getQuery()->getResult();
    }

    public function canUserEditPost(User $user, Post $post): bool
    {
        return $post->getUser() === $user || $user->isAdmin();
    }
}
