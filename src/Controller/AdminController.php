<?php

namespace App\Controller;

use App\Entity\User;
use App\Repository\PostRepository;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin')]
#[IsGranted('ROLE_ADMIN')]
class AdminController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private PostRepository $postRepository,
        private UserRepository $userRepository
    ) {
    }

    #[Route('/', name: 'app_admin')]
    public function index(): Response
    {
        $stats = [
            'total_users' => $this->userRepository->count([]),
            'admin_users' => $this->userRepository->countAdmins(),
            'total_posts' => $this->postRepository->count([]),
            'active_posts' => $this->postRepository->count(['isActive' => true]),
        ];

        return $this->render('admin/index.html.twig', [
            'stats' => $stats,
        ]);
    }

    #[Route('/users', name: 'app_admin_users')]
    public function users(): Response
    {
        $users = $this->userRepository->findAllOrderedByDate();

        return $this->render('admin/users.html.twig', [
            'users' => $users,
        ]);
    }

    #[Route('/users/{id}/toggle-admin', name: 'app_admin_toggle_admin', methods: ['POST'])]
    public function toggleAdmin(User $user, Request $request): Response
    {
        if ($this->isCsrfTokenValid('toggle-admin-' . $user->getId(), $request->request->get('_token'))) {
            $roles = $user->getRoles();

            if (in_array('ROLE_ADMIN', $roles)) {
                // Remove admin role
                $roles = array_filter($roles, fn($role) => $role !== 'ROLE_ADMIN');
                $this->addFlash('success', 'Administrátorská práva byla odebrána uživateli ' . $user->getEmail());
            } else {
                // Add admin role
                $roles[] = 'ROLE_ADMIN';
                $this->addFlash('success', 'Administrátorská práva byla přidělena uživateli ' . $user->getEmail());
            }

            $user->setRoles(array_values($roles));
            $this->entityManager->flush();
        }

        return $this->redirectToRoute('app_admin_users');
    }

    #[Route('/users/{id}/delete', name: 'app_admin_delete_user', methods: ['POST'])]
    public function deleteUser(User $user, Request $request): Response
    {
        if ($this->isCsrfTokenValid('delete-user-' . $user->getId(), $request->request->get('_token'))) {
            // Prevent deleting yourself
            if ($user === $this->getUser()) {
                $this->addFlash('error', 'Nemůžete smazat sám sebe.');
                return $this->redirectToRoute('app_admin_users');
            }

            $this->entityManager->remove($user);
            $this->entityManager->flush();

            $this->addFlash('success', 'Uživatel ' . $user->getEmail() . ' byl smazán.');
        }

        return $this->redirectToRoute('app_admin_users');
    }
}
