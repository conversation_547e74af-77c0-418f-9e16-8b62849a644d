<?php

namespace App\Controller;

use App\Entity\Post;
use App\Form\PostFormType;
use App\Service\PostService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use App\Entity\PostImage;
use App\Entity\Category;
use App\Entity\Breed;

#[Route('/moje-inzeraty')]
#[IsGranted('ROLE_USER')]
class UserPostController extends AbstractController
{
    public function __construct(
        private PostService $postService,
        private EntityManagerInterface $entityManager
    ) {
    }

    #[Route('', name: 'app_user_posts')]
    public function index(): Response
    {
        $user = $this->getUser();
        $posts = $this->postService->getUserPosts($user, true);

        return $this->render('user_post/index.html.twig', [
            'posts' => $posts,
        ]);
    }

    #[Route('/novy', name: 'app_user_post_new')]
    public function new(Request $request): Response
    {
        $post = new Post();
        $form = $this->createForm(PostFormType::class, $post);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $uploadedFiles = $form->get('images')->getData();

            $this->postService->createPost($post, $this->getUser(), $uploadedFiles);

            $this->addFlash('success', 'Inzerát byl úspěšně vytvořen.');

            return $this->redirectToRoute('app_user_posts');
        }

        return $this->render('user_post/new.html.twig', [
            'post' => $post,
            'form' => $form,
        ]);
    }

    #[Route('/{id}/upravit', name: 'app_user_post_edit', requirements: ['id' => '\d+'])]
    public function edit(Request $request, Post $post): Response
    {
        if (!$this->postService->canUserEditPost($this->getUser(), $post)) {
            throw $this->createAccessDeniedException('Nemáte oprávnění upravovat tento inzerát.');
        }

        $form = $this->createForm(PostFormType::class, $post);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $uploadedFiles = $form->get('images')->getData();

            $this->postService->updatePost($post, $uploadedFiles);

            $this->addFlash('success', 'Inzerát byl úspěšně upraven.');

            return $this->redirectToRoute('app_user_posts');
        }

        return $this->render('user_post/edit.html.twig', [
            'post' => $post,
            'form' => $form,
        ]);
    }

    #[Route('/{id}/archivovat', name: 'app_user_post_archive', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function archive(Request $request, Post $post): Response
    {
        if (!$this->postService->canUserEditPost($this->getUser(), $post)) {
            throw $this->createAccessDeniedException('Nemáte oprávnění archivovat tento inzerát.');
        }

        if ($this->isCsrfTokenValid('archive' . $post->getId(), $request->request->get('_token'))) {
            $this->postService->archivePost($post);
            $this->addFlash('success', 'Inzerát byl archivován.');
        } else {
            $this->addFlash('error', 'Neplatný CSRF token.');
        }

        return $this->redirectToRoute('app_user_posts');
    }

    #[Route('/{id}/obnovit', name: 'app_user_post_unarchive', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function unarchive(Request $request, Post $post): Response
    {
        if (!$this->postService->canUserEditPost($this->getUser(), $post)) {
            throw $this->createAccessDeniedException('Nemáte oprávnění obnovit tento inzerát.');
        }

        if ($this->isCsrfTokenValid('unarchive' . $post->getId(), $request->request->get('_token'))) {
            $this->postService->unarchivePost($post);
            $this->addFlash('success', 'Inzerát byl obnoven.');
        } else {
            $this->addFlash('error', 'Neplatný CSRF token.');
        }

        return $this->redirectToRoute('app_user_posts');
    }

    #[Route('/{id}/smazat', name: 'app_user_post_delete', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function delete(Request $request, Post $post): Response
    {
        if (!$this->postService->canUserEditPost($this->getUser(), $post)) {
            throw $this->createAccessDeniedException('Nemáte oprávnění smazat tento inzerát.');
        }

        if ($this->isCsrfTokenValid('delete' . $post->getId(), $request->request->get('_token'))) {
            $this->postService->deletePost($post);
            $this->addFlash('success', 'Inzerát byl smazán.');
        } else {
            $this->addFlash('error', 'Neplatný CSRF token.');
        }

        return $this->redirectToRoute('app_user_posts');
    }

    #[Route('/{id}/prepnout-stav', name: 'app_user_post_toggle_status', requirements: ['id' => '\d+'], methods: ['POST'])]
    public function toggleStatus(Request $request, Post $post): Response
    {
        if (!$this->postService->canUserEditPost($this->getUser(), $post)) {
            throw $this->createAccessDeniedException('Nemáte oprávnění změnit stav tohoto inzerátu.');
        }

        if ($this->isCsrfTokenValid('toggle_status' . $post->getId(), $request->request->get('_token'))) {
            $post->setIsActive(!$post->isActive());
            $this->entityManager->flush();

            $message = $post->isActive() ? 'Inzerát byl aktivován.' : 'Inzerát byl deaktivován.';
            $this->addFlash('success', $message);
        } else {
            $this->addFlash('error', 'Neplatný CSRF token.');
        }

        return $this->redirectToRoute('app_user_posts');
    }

    #[Route('/image/{id}/remove', name: 'app_user_post_image_remove', requirements: ['id' => '\d+'], methods: ['DELETE'])]
    public function removeImage(Request $request, PostImage $image): JsonResponse
    {
        $post = $image->getPost();

        if (!$this->postService->canUserEditPost($this->getUser(), $post)) {
            return new JsonResponse(['error' => 'Nemáte oprávnění smazat tuto fotografii.'], 403);
        }

        if ($this->isCsrfTokenValid('remove_image' . $image->getId(), $request->headers->get('X-CSRF-Token'))) {
            // Remove image file from filesystem
            $uploadsDirectory = $this->getParameter('kernel.project_dir') . '/public/uploads/posts';
            $filePath = $uploadsDirectory . '/' . $image->getImageUrl();
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            // Remove from database
            $this->entityManager->remove($image);
            $this->entityManager->flush();

            return new JsonResponse(['success' => true]);
        }

        return new JsonResponse(['error' => 'Neplatný CSRF token.'], 400);
    }

    #[Route('/api/breeds/{categoryId}', name: 'app_user_post_api_breeds', requirements: ['categoryId' => '\d+'], methods: ['GET'])]
    public function getBreedsByCategory(int $categoryId): JsonResponse
    {
        $category = $this->entityManager->getRepository(Category::class)->find($categoryId);

        if (!$category) {
            return new JsonResponse(['error' => 'Kategorie nebyla nalezena.'], 404);
        }

        $breeds = $this->entityManager->getRepository(Breed::class)->findBy(
            ['category' => $category],
            ['name' => 'ASC']
        );

        $breedData = [];
        foreach ($breeds as $breed) {
            $breedData[] = [
                'id' => $breed->getId(),
                'name' => $breed->getName()
            ];
        }

        return new JsonResponse($breedData);
    }
}
