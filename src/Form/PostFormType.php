<?php

namespace App\Form;

use App\Entity\Post;
use App\Entity\Category;
use App\Entity\Region;
use App\Entity\Breed;
use App\Entity\Enum\PostType;
use App\Entity\Enum\Country;
use App\Entity\Enum\PriceType;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\EnumType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\All;
use Symfony\Component\Validator\Constraints\Count;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Length;

class PostFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', TextType::class, [
                'label' => 'Název inzerátu',
                'constraints' => [
                    new NotBlank(['message' => 'Název inzerátu je povinný.']),
                    new Length([
                        'min' => 5,
                        'max' => 200,
                        'minMessage' => 'Název musí mít alespoň {{ limit }} znaků.',
                        'maxMessage' => 'Název může mít maximálně {{ limit }} znaků.'
                    ])
                ],
                'attr' => [
                    'class' => 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500',
                    'placeholder' => 'Zadejte název inzerátu'
                ]
            ])
            ->add('postType', EnumType::class, [
                'class' => PostType::class,
                'label' => 'Typ inzerátu',
                'choice_label' => fn(PostType $postType) => $postType->getLabel(),
                'attr' => [
                    'class' => 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500'
                ]
            ])
            ->add('country', EnumType::class, [
                'class' => Country::class,
                'label' => 'Země',
                'choice_label' => fn(Country $country) => $country->getLabel(),
                'attr' => [
                    'class' => 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500'
                ]
            ])
            ->add('region', EntityType::class, [
                'class' => Region::class,
                'label' => 'Kraj',
                'choice_label' => 'name',
                'required' => false,
                'placeholder' => 'Vyberte kraj',
                'attr' => [
                    'class' => 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500'
                ]
            ])
            ->add('category', EntityType::class, [
                'class' => Category::class,
                'label' => 'Kategorie',
                'choice_label' => 'name',
                'placeholder' => 'Vyberte kategorii',
                'attr' => [
                    'class' => 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500'
                ]
            ])
            ->add('breed', EntityType::class, [
                'class' => Breed::class,
                'label' => 'Plemeno',
                'choice_label' => 'name',
                'required' => false,
                'placeholder' => 'Vyberte plemeno',
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('b')
                        ->orderBy('b.name', 'ASC');
                },
                'attr' => [
                    'class' => 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500'
                ]
            ])
            ->add('priceType', EnumType::class, [
                'class' => PriceType::class,
                'label' => 'Typ ceny',
                'choice_label' => fn(PriceType $priceType) => $priceType->getLabel(),
                'required' => false,
                'placeholder' => 'Vyberte typ ceny',
                'attr' => [
                    'class' => 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500'
                ]
            ])
            ->add('priceAmount', MoneyType::class, [
                'label' => 'Cena',
                'required' => false,
                'currency' => 'CZK',
                'attr' => [
                    'class' => 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500',
                    'placeholder' => '0'
                ]
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Popis',
                'constraints' => [
                    new NotBlank(['message' => 'Popis je povinný.']),
                    new Length([
                        'min' => 20,
                        'max' => 2000,
                        'minMessage' => 'Popis musí mít alespoň {{ limit }} znaků.',
                        'maxMessage' => 'Popis může mít maximálně {{ limit }} znaků.'
                    ])
                ],
                'attr' => [
                    'class' => 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500',
                    'rows' => 6,
                    'placeholder' => 'Popište své zvíře...'
                ]
            ])
            ->add('contactPhone', TextType::class, [
                'label' => 'Kontaktní telefon',
                'required' => false,
                'attr' => [
                    'class' => 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500',
                    'placeholder' => '+420 123 456 789'
                ]
            ])
            ->add('images', FileType::class, [
                'label' => 'Fotografie',
                'multiple' => true,
                'mapped' => false,
                'required' => false,
                'constraints' => [
                    new All([
                        new File([
                            'maxSize' => '5M',
                            'mimeTypes' => [
                                'image/jpeg',
                                'image/png',
                                'image/webp'
                            ],
                            'mimeTypesMessage' => 'Nahrajte platný obrázek (JPEG, PNG, WebP)',
                        ])
                    ]),
                    new Count([
                        'max' => 10,
                        'maxMessage' => 'Můžete nahrát maximálně {{ limit }} fotografií'
                    ])
                ],
                'attr' => [
                    'class' => 'mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100',
                    'accept' => 'image/*',
                    'multiple' => true
                ]
            ]);

        // Dynamic breed filtering based on category
        $builder->addEventListener(FormEvents::PRE_SET_DATA, function (FormEvent $event) {
            $post = $event->getData();
            $form = $event->getForm();

            if ($post && $post->getCategory()) {
                $this->addBreedField($form, $post->getCategory());
            }
        });

        $builder->get('category')->addEventListener(FormEvents::POST_SUBMIT, function (FormEvent $event) {
            $category = $event->getForm()->getData();
            $form = $event->getForm()->getParent();

            $this->addBreedField($form, $category);
        });
    }

    private function addBreedField(FormInterface $form, ?Category $category): void
    {
        $form->add('breed', EntityType::class, [
            'class' => Breed::class,
            'label' => 'Plemeno',
            'choice_label' => 'name',
            'required' => false,
            'placeholder' => 'Vyberte plemeno',
            'query_builder' => function (EntityRepository $er) use ($category) {
                $qb = $er->createQueryBuilder('b')
                    ->orderBy('b.name', 'ASC');

                if ($category) {
                    $qb->andWhere('b.category = :category')
                       ->setParameter('category', $category);
                }

                return $qb;
            },
            'attr' => [
                'class' => 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500'
            ]
        ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Post::class,
        ]);
    }
}
